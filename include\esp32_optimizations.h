#ifndef ESP32_OPTIMIZATIONS_H
#define ESP32_OPTIMIZATIONS_H

#include <Arduino.h>
#include <esp_system.h>
#include <esp_wifi.h>
#include <esp_bt.h>
#include <driver/adc.h>
#include <esp_adc_cal.h>
#include <soc/soc.h>
#include <soc/rtc_cntl_reg.h>

// ============================================================================
// ESP32 Specific Optimizations and Utilities
// ============================================================================

class ESP32Optimizations {
private:
  static esp_adc_cal_characteristics_t adc_chars_voltage;
  static esp_adc_cal_characteristics_t adc_chars_current;
  static bool adc_calibrated;
  
public:
  // System initialization optimizations
  static void initializeESP32();
  static void disableUnusedPeripherals();
  static void optimizeWiFiSettings();
  static void setupPowerManagement();
  
  // ADC optimizations
  static void calibrateADC();
  static uint32_t readVoltageCalibrated(adc1_channel_t channel);
  static uint32_t readCurrentCalibrated(adc1_channel_t channel);
  
  // Memory management
  static void printMemoryInfo();
  static bool checkMemoryHealth();
  static void optimizeHeap();
  
  // Performance monitoring
  static void printCPUInfo();
  static void printTaskInfo();
  static uint32_t getFreeStackSpace();
  
  // Watchdog management
  static void feedTaskWatchdog();
  static void setupCustomWatchdog();
  
  // Deep sleep utilities (for future power saving)
  static void prepareForDeepSleep();
  static void wakeupFromDeepSleep();
  
  // Error handling
  static void handlePanicRestart();
  static void logSystemRestart();
};

// ============================================================================
// ESP32 Hardware Abstraction Layer
// ============================================================================

class ESP32HAL {
public:
  // GPIO optimizations
  static void configureGPIOForADC(gpio_num_t pin);
  static void setGPIODriveStrength(gpio_num_t pin, gpio_drive_cap_t strength);
  
  // Timer utilities
  static void setupHighResolutionTimer();
  static uint64_t getMicroseconds();
  
  // Flash memory utilities
  static bool writeToNVS(const char* key, const void* data, size_t length);
  static bool readFromNVS(const char* key, void* data, size_t length);
  
  // Temperature monitoring
  static float getCPUTemperature();
  static bool isTemperatureNormal();
};

// ============================================================================
// ESP32 Specific Macros and Constants
// ============================================================================

// CPU frequency options
#define ESP32_CPU_FREQ_240MHZ   240
#define ESP32_CPU_FREQ_160MHZ   160
#define ESP32_CPU_FREQ_80MHZ    80

// ADC calibration constants
#define ESP32_ADC_VREF_DEFAULT  1100  // mV
#define ESP32_ADC_SAMPLES_AVG   64    // Number of samples for averaging

// Memory thresholds
#define ESP32_MIN_FREE_HEAP     50000  // Minimum free heap in bytes
#define ESP32_HEAP_WARNING      20000  // Warning threshold for heap

// Task priorities (ESP32 specific)
#define ESP32_TASK_PRIORITY_HIGH    3
#define ESP32_TASK_PRIORITY_NORMAL  2
#define ESP32_TASK_PRIORITY_LOW     1

// Stack sizes for tasks
#define ESP32_STACK_SIZE_LARGE      8192
#define ESP32_STACK_SIZE_MEDIUM     4096
#define ESP32_STACK_SIZE_SMALL      2048

// ============================================================================
// ESP32 Performance Monitoring Macros
// ============================================================================

#ifdef DEBUG
  #define ESP32_PERFORMANCE_START(name) \
    unsigned long perf_start_##name = micros()
    
  #define ESP32_PERFORMANCE_END(name) \
    unsigned long perf_end_##name = micros(); \
    Serial.printf("Performance [%s]: %lu us\n", #name, perf_end_##name - perf_start_##name)
    
  #define ESP32_MEMORY_CHECK() \
    if (ESP.getFreeHeap() < ESP32_HEAP_WARNING) { \
      Serial.printf("WARNING: Low heap memory: %d bytes\n", ESP.getFreeHeap()); \
    }
#else
  #define ESP32_PERFORMANCE_START(name)
  #define ESP32_PERFORMANCE_END(name)
  #define ESP32_MEMORY_CHECK()
#endif

// ============================================================================
// ESP32 Utility Functions
// ============================================================================

// Fast GPIO operations
#define ESP32_GPIO_SET_HIGH(pin)    GPIO.out_w1ts = (1 << pin)
#define ESP32_GPIO_SET_LOW(pin)     GPIO.out_w1tc = (1 << pin)
#define ESP32_GPIO_READ(pin)        ((GPIO.in >> pin) & 0x1)

// Fast ADC reading (bypass Arduino framework)
#define ESP32_ADC_READ_FAST(channel) adc1_get_raw(channel)

// Memory alignment for better performance
#define ESP32_ALIGN_32BIT           __attribute__((aligned(4)))
#define ESP32_ALIGN_CACHE_LINE      __attribute__((aligned(32)))

// ============================================================================
// ESP32 Configuration Validation
// ============================================================================

// Compile-time checks for ESP32 compatibility
#ifndef ESP32
  #error "This code is designed specifically for ESP32"
#endif

#if CONFIG_FREERTOS_UNICORE
  #warning "Single core mode detected - some optimizations may not be available"
#endif

#ifndef CONFIG_SPIRAM_SUPPORT
  #warning "PSRAM not enabled - memory may be limited"
#endif

// Validate GPIO pin assignments for ESP32 ADC1
// GPIO36 and GPIO39 are valid ADC1 channels, so no warnings needed for our configuration
#if defined(VOLTAGE_SENSOR_PIN) && (VOLTAGE_SENSOR_PIN < 32 || VOLTAGE_SENSOR_PIN > 39)
  #pragma message "Note: Voltage sensor pin is outside ADC1 range (GPIO32-39)"
#endif

#if defined(CURRENT_SENSOR_PIN) && (CURRENT_SENSOR_PIN < 32 || CURRENT_SENSOR_PIN > 39)
  #pragma message "Note: Current sensor pin is outside ADC1 range (GPIO32-39)"
#endif

// ============================================================================
// ESP32 Debugging Utilities
// ============================================================================

#ifdef DEBUG
  #define ESP32_DEBUG_HEAP() \
    Serial.printf("Heap: Free=%d, Min=%d, Max=%d\n", \
                  ESP.getFreeHeap(), ESP.getMinFreeHeap(), ESP.getMaxAllocHeap())
                  
  #define ESP32_DEBUG_TASK_STACK() \
    Serial.printf("Stack: Free=%d bytes\n", uxTaskGetStackHighWaterMark(NULL))
    
  #define ESP32_DEBUG_CPU_USAGE() \
    Serial.printf("CPU: Freq=%dMHz, Temp=%.1f°C\n", \
                  ESP.getCpuFreqMHz(), temperatureRead())
#else
  #define ESP32_DEBUG_HEAP()
  #define ESP32_DEBUG_TASK_STACK()
  #define ESP32_DEBUG_CPU_USAGE()
#endif

// Global instance
extern ESP32Optimizations esp32opt;
extern ESP32HAL esp32hal;

#endif // ESP32_OPTIMIZATIONS_H
