# 🚀 دليل البدء السريع - مراقب الطاقة ESP32

اجعل نظام مراقبة الطاقة يعمل في 30 دقيقة!

## 📦 ما تحتاجه

### قائمة فحص الأجهزة
- [ ] ESP32 DevKit v1
- [ ] وحدة مستشعر الجهد ZMPT101B
- [ ] وحدة مستشعر التيار ACS712-5A
- [ ] لوحة تجارب وأسلاك توصيل
- [ ] كابل USB لـ ESP32
- [ ] مصدر طاقة 5V (اختياري)

### قائمة فحص البرامج
- [ ] PlatformIO IDE أو VS Code مع إضافة PlatformIO
- [ ] تطبيق Blynk IoT للهاتف
- [ ] شبكة WiFi (2.4GHz)

## ⚡ إعداد الأجهزة في 5 دقائق

### الخطوة 1: توصيل المستشعرات
```
ESP32 → ZMPT101B:
• 3.3V → VCC
• GND → GND  
• GPIO36 → OUT

ESP32 → ACS712:
• 5V → VCC
• GND → GND
• GPIO39 → OUT
```

### الخطوة 2: توصيل خطوط الطاقة
```
⚠️ الأمان أولاً: أوقف الطاقة قبل التوصيل!

توصيلات ZMPT101B AC:
• AC1 → السلك الحي (من القاطع)
• AC2 → السلك المحايد

مسار تيار ACS712:
• IP+ → السلك الحي (إلى الحمل)
• IP- → السلك الحي (من الحمل)
```

## 💻 إعداد البرنامج في 5 دقائق

### الخطوة 1: رفع البرنامج الثابت
```bash
# فتح الطرفية في دليل المشروع
cd VOLT

# البناء والرفع
pio run --target upload

# مراقبة الإخراج
pio device monitor
```

### الخطوة 2: إعداد WiFi
1. تشغيل ESP32
2. توصيل الهاتف/الكمبيوتر المحمول بـ WiFi "PowerMonitor-Setup"
3. فتح المتصفح → ***********
4. إدخال بيانات اعتماد WiFi الخاصة بك
5. النقر على "حفظ" والانتظار لإعادة التشغيل

## 📱 إعداد Blynk في 5 دقائق

### الخطوة 1: إنشاء حساب Blynk
1. تحميل تطبيق Blynk IoT
2. التسجيل في https://blynk.cloud
3. إنشاء جهاز جديد → ESP32
4. نسخ رمز المصادقة

### الخطوة 2: إضافة الأدوات
إعداد الأدوات السريع:

| الأداة | الدبوس | الإعدادات |
|--------|-----|----------|
| مقياس | V0 | 0-300V، "الجهد" |
| مقياس | V1 | 0-5A، "التيار" |
| مقياس | V2 | 0-1500W، "القدرة" |
| عرض القيمة | V3 | "الطاقة (Wh)" |
| شريط تمرير | V5 | 180-250V، "الحد الأدنى للجهد" |
| شريط تمرير | V6 | 220-280V، "الحد الأقصى للجهد" |
| شريط تمرير | V7 | 0-5A، "الحد الأقصى للتيار" |
| زر | V8 | "إعادة تعيين الطاقة" |

### الخطوة 3: توصيل الجهاز
1. العودة إلى إعداد WiFi (***********)
2. إدخال رمز مصادقة Blynk الخاص بك
3. الحفظ وإعادة التشغيل
4. فحص تطبيق Blynk - يجب أن يكون الجهاز متصلاً!

## 🔧 المعايرة السريعة

### معايرة الجهد
1. قياس الجهد الفعلي بالمتعدد
2. مقارنة مع قراءة التطبيق
3. ضبط `VOLTAGE_CALIBRATION` في config.h إذا لزم الأمر

### معايرة التيار
1. استخدام حمل معروف (مثل مصباح 100W = ~0.45A عند 220V)
2. مقارنة القراءات
3. ضبط `CURRENT_CALIBRATION` إذا لزم الأمر

## ✅ قائمة فحص التحقق

### فحص الأجهزة
- [ ] جميع التوصيلات آمنة
- [ ] لا توجد أسلاك مفكوكة
- [ ] عزل مناسب
- [ ] فيوزات مثبتة
- [ ] ESP32 مشغل
- [ ] LED الحالة يرمش

### فحص البرنامج
- [ ] المراقب التسلسلي يظهر "System Online"
- [ ] WiFi متصل (LED ثابت)
- [ ] Blynk يظهر الجهاز متصل
- [ ] قراءات المستشعر تبدو معقولة
- [ ] يمكن تشغيل التنبيهات

### فحص الأمان
- [ ] جميع توصيلات الجهد العالي معزولة
- [ ] الصندوق مغلق بشكل صحيح
- [ ] إيقاف الطوارئ متاح
- [ ] قاطع الدائرة يعمل
- [ ] حماية GFCI نشطة

## 🚨 استكشاف الأخطاء وإصلاحها

### "WiFi لن يتصل"
```
1. تحقق من شبكة 2.4GHz
2. تحقق من كلمة المرور
3. إعادة تعيين: اضغط GPIO0 أثناء التشغيل
4. جرب شبكة مختلفة
```

### "Blynk غير متصل"
```
1. تحقق من رمز المصادقة
2. تحقق من اتصال الإنترنت
3. إعادة تشغيل ESP32
4. تحقق من حالة خادم Blynk
```

### "قراءات خاطئة"
```
1. تحقق من الأسلاك
2. تحقق من طاقة المستشعر (3.3V/5V)
3. معايرة بقيم معروفة
4. تحقق من التداخل
```

### "لا توجد قراءات"
```
1. تحقق من دبابيس ADC (GPIO36, GPIO39)
2. تحقق من طاقة المستشعر
3. اختبار بالمتعدد
4. تحقق من المراقب التسلسلي للأخطاء
```

## 📊 الاختبار الأول

### إعداد اختبار آمن
1. استخدام حمل بسيط (مصباح LED، 40-100W)
2. البدء بجهد منخفض إذا أمكن
3. مراقبة القراءات في الوقت الفعلي
4. التحقق من الحسابات: P = V × I

### القراءات المتوقعة
```
مصباح LED 100W عند 220V:
• الجهد: ~220V
• التيار: ~0.45A  
• القدرة: ~100W
• الطاقة: تزيد مع الوقت
```

## 🎯 الخطوات التالية

### فوري
- [ ] تعيين عتبات تنبيه مناسبة
- [ ] اختبار الإشعارات الفورية
- [ ] إنشاء خط أساس لاستخدام الطاقة
- [ ] توثيق قيم المعايرة المحددة

### متقدم
- [ ] إضافة مستشعرات أكثر للمراقبة ثلاثية الطور
- [ ] التكامل مع أتمتة المنزل
- [ ] إعداد تسجيل وتحليل البيانات
- [ ] إنشاء موضوعات لوحة تحكم Blynk مخصصة

## 📞 تحتاج مساعدة؟

### إصلاحات سريعة
- إعادة تشغيل ESP32: فصل وإعادة توصيل الطاقة
- إعادة تعيين WiFi: توصيل GPIO0 بـ GND أثناء التشغيل
- إعادة تعيين المصنع: اضغط زر الإعادة لمدة 10 ثوانٍ

### الحصول على الدعم
- تحقق من README.md للوثائق التفصيلية
- مراجعة مخططات الأسلاك
- تحقق من منتديات مجتمع Blynk
- إنشاء مشكلة GitHub للأخطاء

---

**🎉 تهانينا! مراقب الطاقة الخاص بك الآن مباشر!**

راقب استخدام الطاقة، واحصل على تنبيهات للشذوذ، واستمتع براحة البال التي تأتي مع مراقبة الطاقة في الوقت الفعلي.

**تذكر**: دائماً أعط الأولوية للأمان عند العمل مع الأنظمة الكهربائية. عند الشك، استشر كهربائياً مؤهلاً.
