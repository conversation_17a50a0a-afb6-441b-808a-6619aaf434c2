# إعداد بيانات الاعتماد - ESP32 Power Monitor

## 🔑 **بيانات الاعتماد المطلوبة**

قبل بناء ورفع المشروع، تحتاج لتحديث بيانات الاعتماد التالية:

### **1. إعدادات WiFi**

في الملف `src/wifi_manager.cpp` السطر 56-57:

```cpp
const char* ssid = "YourWiFiSSID";         // اسم شبكة WiFi
const char* password = "YourWiFiPassword"; // كلمة مرور WiFi
```

**استبدل:**
- `YourWiFiSSID` باسم شبكة WiFi الخاصة بك
- `YourWiFiPassword` بكلمة مرور الشبكة

**مثال:**
```cpp
const char* ssid = "HomeNetwork";
const char* password = "MySecurePassword123";
```

### **2. إعد<PERSON><PERSON><PERSON> Blynk**

#### **أ) في الملف `include/blynk_config.h`:**

```cpp
#define BLYNK_TEMPLATE_ID "TMPL6XXXXXXXx"              // Template ID من Blynk
#define BLYNK_TEMPLATE_NAME "ESP32 Power Monitor"       // اسم المشروع
#define BLYNK_AUTH_TOKEN "YourAuthTokenHere-32CharactersLong" // Auth Token
```

#### **ب) في الملف `src/wifi_manager.cpp` السطر 115:**

```cpp
blynk_token = "YourBlynkAuthToken32CharactersLong"; // نفس Auth Token
```

## 🚀 **كيفية الحصول على بيانات Blynk**

### **الخطوة 1: إنشاء حساب Blynk**
1. اذهب إلى https://blynk.cloud
2. سجل حساب جديد أو سجل دخول
3. انقر على "Create New Template"

### **الخطوة 2: إعداد Template**
1. **Name**: ESP32 Power Monitor
2. **Hardware**: ESP32
3. **Connection Type**: WiFi
4. انقر "Done"

### **الخطوة 3: الحصول على Template ID**
- ستجد Template ID في أعلى الصفحة
- مثال: `TMPL6abc123def`

### **الخطوة 4: إنشاء Device**
1. اذهب إلى تبويب "Devices"
2. انقر "New Device"
3. اختر Template الذي أنشأته
4. أدخل اسم الجهاز: "PowerMonitor-01"
5. انقر "Create"

### **الخطوة 5: الحصول على Auth Token**
- بعد إنشاء الجهاز، ستحصل على Auth Token
- مثال: `abcd1234efgh5678ijkl9012mnop3456`

## 📱 **إعداد تطبيق Blynk**

### **تحميل التطبيق:**
- **Android**: Google Play Store
- **iOS**: App Store
- ابحث عن "Blynk IoT"

### **إضافة Widgets:**

| Widget | Virtual Pin | الإعدادات |
|--------|-------------|-----------|
| Gauge | V0 | 0-300V, "الجهد" |
| Gauge | V1 | 0-5A, "التيار" |
| Gauge | V2 | 0-1500W, "القدرة" |
| Value Display | V3 | "الطاقة (Wh)" |
| Slider | V5 | 180-250V, "الحد الأدنى للجهد" |
| Slider | V6 | 220-280V, "الحد الأقصى للجهد" |
| Slider | V7 | 0-5A, "الحد الأقصى للتيار" |
| Button | V8 | "إعادة تعيين الطاقة" |
| Terminal | V9 | "سجل النظام" |

## 🔧 **مثال كامل للإعدادات**

### **ملف `src/wifi_manager.cpp`:**
```cpp
// السطر 56-57
const char* ssid = "MyHomeWiFi";
const char* password = "MyWiFiPassword123";

// السطر 115
blynk_token = "abcd1234efgh5678ijkl9012mnop3456";
```

### **ملف `include/blynk_config.h`:**
```cpp
#define BLYNK_TEMPLATE_ID "TMPL6abc123def"
#define BLYNK_TEMPLATE_NAME "ESP32 Power Monitor"
#define BLYNK_AUTH_TOKEN "abcd1234efgh5678ijkl9012mnop3456"
```

## ⚠️ **ملاحظات مهمة**

### **أمان البيانات:**
- لا تشارك Auth Token مع أحد
- استخدم كلمة مرور قوية للـ WiFi
- احتفظ بنسخة احتياطية من البيانات

### **استكشاف الأخطاء:**
- تأكد من صحة اسم الشبكة وكلمة المرور
- تأكد من أن Auth Token صحيح (32 حرف)
- تأكد من أن الشبكة 2.4GHz (ESP32 لا يدعم 5GHz)

### **اختبار الاتصال:**
1. ارفع الكود إلى ESP32
2. افتح Serial Monitor
3. يجب أن ترى:
   ```
   WiFi connected! IP: *************
   Blynk connected successfully
   ```

## 🎯 **الخطوات التالية**

بعد تحديث البيانات:

1. **احفظ جميع الملفات**
2. **ابني المشروع:**
   ```bash
   pio run
   ```
3. **ارفع إلى ESP32:**
   ```bash
   pio run --target upload
   ```
4. **راقب الإخراج:**
   ```bash
   pio device monitor
   ```

## 🔄 **إذا لم يعمل الاتصال**

### **مشاكل WiFi:**
- تحقق من اسم الشبكة وكلمة المرور
- تأكد من أن الشبكة 2.4GHz
- جرب الاقتراب من الراوتر

### **مشاكل Blynk:**
- تحقق من Auth Token
- تأكد من أن الجهاز "Online" في Blynk Console
- جرب إعادة إنشاء الجهاز

---

**بعد تحديث هذه البيانات، سيكون النظام جاهز للعمل! 🚀**
