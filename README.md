# ESP32 Power Monitoring System

A comprehensive real-time power monitoring solution using ESP32 microcontroller with cloud integration and mobile app support.

## 🚀 Features

- **Real-time Monitoring**: Voltage, current, power, and energy consumption tracking
- **Smart Sensors**: ZMPT101B voltage sensor and ACS712 current sensor support
- **WiFi Management**: Automatic connection with captive portal configuration
- **Cloud Integration**: Blynk IoT Cloud for remote monitoring and control
- **Mobile App**: Real-time charts, gauges, and push notifications
- **Alert System**: Configurable thresholds with immediate notifications
- **Energy Tracking**: Cumulative energy consumption logging
- **Robust Design**: Automatic recovery, watchdog timer, and error handling

## 📋 Hardware Requirements

### Core Components
- **ESP32 DevKit v1** - Main microcontroller
- **ZMPT101B** - AC voltage sensor module
- **ACS712-5A** - AC/DC current sensor module
- **Breadboard and jumper wires**
- **Power supply** (5V/3.3V)

### Optional Components
- **Enclosure** for protection
- **Terminal blocks** for permanent connections
- **Fuses** for safety

## 🔌 Wiring Diagram

```
ESP32 DevKit v1 Connections:
┌─────────────────────────────────────────┐
│                ESP32                    │
│                                         │
│  GPIO36 (A0) ←→ ZMPT101B Signal         │
│  GPIO39 (A3) ←→ ACS712 Signal           │
│  GPIO2       ←→ Built-in LED            │
│  3.3V        ←→ Sensors VCC             │
│  GND         ←→ Sensors GND             │
└─────────────────────────────────────────┘

ZMPT101B Voltage Sensor:
┌─────────────────┐
│    ZMPT101B     │
│                 │
│  VCC ←→ 3.3V    │
│  GND ←→ GND     │
│  OUT ←→ GPIO36  │
│  AC1 ←→ Live    │
│  AC2 ←→ Neutral │
└─────────────────┘

ACS712 Current Sensor:
┌─────────────────┐
│     ACS712      │
│                 │
│  VCC ←→ 5V      │
│  GND ←→ GND     │
│  OUT ←→ GPIO39  │
│  IP+ ←→ Load +  │
│  IP- ←→ Load -  │
└─────────────────┘
```

## 🛠️ Software Setup

### 1. PlatformIO Installation
```bash
# Install PlatformIO Core
pip install platformio

# Or use PlatformIO IDE extension in VS Code
```

### 2. Project Setup
```bash
# Clone or download the project
git clone <repository-url>
cd VOLT

# Build the project
pio run

# Upload to ESP32
pio run --target upload

# Monitor serial output
pio device monitor
```

### 3. WiFi Configuration
1. Power on the ESP32
2. Connect to WiFi network "PowerMonitor-Setup" (password: ********)
3. Open browser and go to ***********
4. Configure your WiFi credentials and Blynk token
5. Save configuration and restart

### 4. Blynk App Setup

#### Create Blynk Account
1. Download Blynk IoT app from App Store/Google Play
2. Create account at https://blynk.cloud
3. Create new device and copy the auth token

#### Configure Widgets
Add these widgets to your Blynk dashboard:

| Widget | Virtual Pin | Description |
|--------|-------------|-------------|
| Gauge | V0 | Real-time voltage display |
| Gauge | V1 | Real-time current display |
| Gauge | V2 | Real-time power display |
| SuperChart | V0,V1,V2 | Historical data charts |
| Labeled Value | V3 | Energy consumption |
| Slider | V5 | Minimum voltage threshold |
| Slider | V6 | Maximum voltage threshold |
| Slider | V7 | Maximum current threshold |
| Button | V8 | Reset energy counter |
| Terminal | V9 | System logs and commands |

## ⚙️ Configuration

### Sensor Calibration
The system includes automatic calibration, but you can fine-tune:

```cpp
// In config.h, adjust these values:
#define VOLTAGE_CALIBRATION   234.26  // Voltage sensor calibration
#define CURRENT_CALIBRATION   185.0   // Current sensor calibration (mV/A)
#define VOLTAGE_OFFSET        2500    // Voltage sensor zero offset
#define CURRENT_OFFSET        2500    // Current sensor zero offset
```

### Alert Thresholds
Default safety thresholds (configurable via Blynk app):

```cpp
#define VOLTAGE_MIN_THRESHOLD 200.0   // Minimum voltage (V)
#define VOLTAGE_MAX_THRESHOLD 250.0   // Maximum voltage (V)
#define CURRENT_MAX_THRESHOLD 4.0     // Maximum current (A)
#define POWER_MAX_THRESHOLD   1000.0  // Maximum power (W)
```

## 📱 Mobile App Features

### Real-time Monitoring
- Live voltage, current, and power readings
- Historical data charts with zoom and pan
- Energy consumption tracking
- System status indicators

### Remote Control
- Adjustable safety thresholds
- Energy counter reset
- System restart command
- Configuration backup/restore

### Notifications
- Push notifications for threshold violations
- Email alerts for critical events
- SMS notifications (premium feature)
- Custom alert messages

## 🔧 Troubleshooting

### Common Issues

#### WiFi Connection Problems
```
Symptoms: Cannot connect to WiFi
Solutions:
1. Check WiFi credentials in captive portal
2. Ensure 2.4GHz network (ESP32 doesn't support 5GHz)
3. Reset WiFi settings: Connect GPIO0 to GND during boot
4. Check router compatibility
```

#### Sensor Reading Issues
```
Symptoms: Incorrect or unstable readings
Solutions:
1. Check wiring connections
2. Verify sensor power supply (3.3V for ZMPT101B, 5V for ACS712)
3. Calibrate sensors using known loads
4. Check for electromagnetic interference
5. Ensure proper grounding
```

#### Blynk Connection Problems
```
Symptoms: WiFi connected but Blynk offline
Solutions:
1. Verify auth token in configuration
2. Check Blynk server status
3. Ensure device is online in Blynk console
4. Reset Blynk connection in terminal widget
```

### Serial Monitor Commands
Use the terminal widget in Blynk app:
- `status` - Show system status
- `reset` - Restart the system
- `calibrate` - Recalibrate sensors

## 📊 Performance Specifications

- **Voltage Range**: 50V - 300V AC
- **Current Range**: 0A - 5A (ACS712-5A)
- **Accuracy**: ±2% (after calibration)
- **Sampling Rate**: 100 samples/second
- **Update Rate**: 1 second (sensors), 2 seconds (Blynk)
- **WiFi Range**: Standard 802.11 b/g/n
- **Power Consumption**: ~200mA @ 5V

## 🔒 Safety Considerations

⚠️ **WARNING: This system involves high voltage AC power. Follow safety guidelines:**

1. **Electrical Safety**
   - Turn off power before making connections
   - Use proper insulation and enclosures
   - Install appropriate fuses and circuit breakers
   - Follow local electrical codes

2. **Installation Safety**
   - Use qualified electrician for permanent installation
   - Test all connections before energizing
   - Ensure proper grounding
   - Use GFCI protection where required

3. **Operational Safety**
   - Monitor system regularly
   - Respond to alerts immediately
   - Maintain backup monitoring systems
   - Keep emergency contacts available

## 📈 Future Enhancements

- **Multi-phase monitoring** support
- **Power quality analysis** (harmonics, power factor)
- **Load forecasting** using machine learning
- **Integration** with home automation systems
- **Data export** to CSV/Excel formats
- **Advanced analytics** and reporting

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review Blynk documentation
- ESP32 community forums

---

**Built with ❤️ for reliable power monitoring**
