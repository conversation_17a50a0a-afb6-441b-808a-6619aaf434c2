#ifndef GLOBALS_H
#define GLOBALS_H

// ============================================================================
// Global Declarations for ESP32 Power Monitor
// ============================================================================

// Forward declarations to avoid circular dependencies
class PowerSensors;
class PowerWiFiManager;
class PowerBlynkManager;
class AlertSystem;
class ESP32Optimizations;
class ESP32HAL;

// Global instances (defined in their respective .cpp files)
extern PowerSensors sensors;
extern PowerWiFiManager wifiMgr;
extern PowerBlynkManager blynkMgr;
extern AlertSystem alertSys;
extern ESP32Optimizations esp32opt;
extern ESP32HAL esp32hal;

#endif // GLOBALS_H
