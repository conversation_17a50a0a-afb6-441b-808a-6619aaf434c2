# 🔧 دليل الإعدادات والتخصيص - مراقب الطاقة ESP32

دليل شامل لتخصيص وإعداد نظام مراقبة الطاقة حسب احتياجاتك.

## ⚙️ إعدادات المستشعرات

### مستشعر الجهد ZMPT101B

#### المعايرة الأساسية
```cpp
// في ملف include/config.h
#define VOLTAGE_CALIBRATION   234.26  // عامل المعايرة للجهد
#define VOLTAGE_OFFSET        2500    // إزاحة ADC (mV) للجهد الصفري
#define VOLTAGE_SAMPLES       100     // عدد العينات لحساب RMS
```

#### معايرة دقيقة
1. **قياس الجهد المرجعي**: استخدم متعدد دقيق لقياس الجهد الفعلي
2. **مقارنة القراءات**: قارن مع قراءة النظام
3. **حساب عامل التصحيح**:
   ```
   عامل_المعايرة_الجديد = عامل_المعايرة_الحالي × (الجهد_الفعلي / الجهد_المقروء)
   ```

#### مثال عملي
```cpp
// إذا كان الجهد الفعلي 220V والنظام يقرأ 215V:
// عامل_المعايرة_الجديد = 234.26 × (220 / 215) = 239.53
#define VOLTAGE_CALIBRATION   239.53
```

### مستشعر التيار ACS712

#### أنواع ACS712 المختلفة
```cpp
// ACS712-5A (الأكثر شيوعاً للاستخدام المنزلي)
#define CURRENT_CALIBRATION   185.0   // mV/A

// ACS712-20A (للأحمال الأكبر)
#define CURRENT_CALIBRATION   100.0   // mV/A

// ACS712-30A (للأحمال الصناعية)
#define CURRENT_CALIBRATION   66.0    // mV/A
```

#### معايرة التيار
1. **استخدام حمل معروف**:
   ```
   مصباح 100W عند 220V = 100/220 = 0.45A
   مصباح 60W عند 220V = 60/220 = 0.27A
   ```

2. **حساب عامل التصحيح**:
   ```cpp
   // إذا كان التيار الفعلي 0.45A والنظام يقرأ 0.42A:
   // عامل_المعايرة_الجديد = 185.0 × (0.45 / 0.42) = 198.2
   #define CURRENT_CALIBRATION   198.2
   ```

## 🚨 إعدادات التنبيهات

### عتبات الأمان
```cpp
// عتبات الجهد (فولت)
#define VOLTAGE_MIN_THRESHOLD 200.0   // تنبيه انخفاض الجهد
#define VOLTAGE_MAX_THRESHOLD 250.0   // تنبيه ارتفاع الجهد

// عتبة التيار (أمبير)
#define CURRENT_MAX_THRESHOLD 4.0     // تنبيه ارتفاع التيار

// عتبة القدرة (واط)
#define POWER_MAX_THRESHOLD   1000.0  // تنبيه ارتفاع القدرة
```

### إعدادات التنبيه المتقدمة
```cpp
// فترة التهدئة بين التنبيهات (مللي ثانية)
#define ALERT_COOLDOWN_PERIOD 60000   // دقيقة واحدة

// عدد القراءات المستقرة قبل الإرسال
#define MIN_STABLE_READINGS   5       // 5 قراءات مستقرة

// تأخير الاسترداد من الأخطاء
#define ERROR_RECOVERY_DELAY  5000    // 5 ثوانٍ
```

## 📡 إعدادات الشبكة

### إعدادات WiFi
```cpp
// مهلة بوابة الإعداد (ثواني)
#define WIFI_CONFIG_PORTAL_TIMEOUT 180  // 3 دقائق

// اسم نقطة الوصول للإعداد
#define WIFI_AP_NAME          "PowerMonitor-Setup"
#define WIFI_AP_PASSWORD      "12345678"

// اسم المضيف
#define WIFI_HOSTNAME         "ESP32-PowerMonitor"
```

### إعدادات Blynk
```cpp
// فترات الإرسال
#define BLYNK_SEND_INTERVAL   2000    // إرسال البيانات كل ثانيتين
#define BLYNK_TIMEOUT         10000   // مهلة الاتصال 10 ثوانٍ

// الخادم الافتراضي
String blynk_server = "blynk.cloud";
int blynk_port = 80;
```

## 🎛️ إعداد الدبابيس الافتراضية

### دبابيس البيانات
```cpp
#define VPIN_VOLTAGE          V0      // الجهد الفوري
#define VPIN_CURRENT          V1      // التيار الفوري
#define VPIN_POWER            V2      // القدرة المحسوبة
#define VPIN_ENERGY           V3      // استهلاك الطاقة
#define VPIN_STATUS           V4      // حالة النظام
```

### دبابيس التحكم
```cpp
#define VPIN_VOLTAGE_MIN      V5      // عتبة الجهد الأدنى
#define VPIN_VOLTAGE_MAX      V6      // عتبة الجهد الأقصى
#define VPIN_CURRENT_MAX      V7      // عتبة التيار الأقصى
#define VPIN_RESET_ENERGY     V8      // زر إعادة تعيين الطاقة
#define VPIN_TERMINAL         V9      // أداة الطرفية
```

## 🔄 إعدادات التوقيت

### فترات القراءة والإرسال
```cpp
// فترة قراءة المستشعرات
#define SENSOR_READ_INTERVAL  1000    // كل ثانية

// فترة إرسال البيانات إلى Blynk
#define BLYNK_SEND_INTERVAL   2000    // كل ثانيتين

// فترة طباعة الحالة
#define STATUS_PRINT_INTERVAL 30000   // كل 30 ثانية
```

### إعدادات مؤقت المراقبة
```cpp
#define WATCHDOG_TIMEOUT      8000    // 8 ثوانٍ
#define WATCHDOG_FEED_INTERVAL 5000   // تغذية كل 5 ثوانٍ
```

## 🎨 إعدادات LED الحالة

### أنماط الوميض
```cpp
#define LED_PATTERN_OFF           0   // مطفأ
#define LED_PATTERN_SOLID         1   // ثابت
#define LED_PATTERN_SLOW_BLINK    2   // وميض بطيء
#define LED_PATTERN_FAST_BLINK    3   // وميض سريع
#define LED_PATTERN_DOUBLE_BLINK  4   // وميض مزدوج
#define LED_PATTERN_TRIPLE_BLINK  5   // وميض ثلاثي
```

### معاني الأنماط
- **مطفأ**: النظام متوقف
- **ثابت**: يعمل بشكل طبيعي
- **وميض بطيء**: التهيئة أو الاسترداد
- **وميض سريع**: خطأ أو تنبيه
- **وميض مزدوج**: اتصال WiFi
- **وميض ثلاثي**: اتصال Blynk

## 📊 إعدادات معالجة البيانات

### مرشحات الإشارة
```cpp
// أوزان المرشح التمريري المنخفض
#define VOLTAGE_FILTER_WEIGHT 0.1     // للجهد
#define CURRENT_FILTER_WEIGHT 0.1     // للتيار

// عدد العينات للحساب
#define VOLTAGE_SAMPLES       100     // عينات الجهد
#define CURRENT_SAMPLES       100     // عينات التيار
```

### التحقق من صحة البيانات
```cpp
// نطاقات القيم المقبولة
#define VOLTAGE_MIN_VALID     50.0    // أقل جهد صالح
#define VOLTAGE_MAX_VALID     300.0   // أعلى جهد صالح
#define CURRENT_MIN_VALID     0.0     // أقل تيار صالح
#define CURRENT_MAX_VALID     10.0    // أعلى تيار صالح
```

## 🛠️ إعدادات التصحيح

### مستويات التصحيح
```cpp
// في platformio.ini
build_flags = 
    -DCORE_DEBUG_LEVEL=3      // مستوى التصحيح
    -DBLYNK_PRINT=Serial      // تصحيح Blynk
    -DBLYNK_DEBUG             // تفعيل تصحيح Blynk
```

### رسائل التصحيح
```cpp
#ifdef DEBUG
  #define DEBUG_PRINT(x)      Serial.print(x)
  #define DEBUG_PRINTLN(x)    Serial.println(x)
  #define DEBUG_PRINTF(...)   Serial.printf(__VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(...)
#endif
```

## 🔧 إعدادات مخصصة للبيئة

### للاستخدام المنزلي
```cpp
#define VOLTAGE_MIN_THRESHOLD 200.0   // 200V
#define VOLTAGE_MAX_THRESHOLD 250.0   // 250V
#define CURRENT_MAX_THRESHOLD 16.0    // 16A (قاطع منزلي نموذجي)
#define POWER_MAX_THRESHOLD   3500.0  // 3.5kW
```

### للاستخدام التجاري
```cpp
#define VOLTAGE_MIN_THRESHOLD 380.0   // 380V (ثلاثي الطور)
#define VOLTAGE_MAX_THRESHOLD 420.0   // 420V
#define CURRENT_MAX_THRESHOLD 63.0    // 63A
#define POWER_MAX_THRESHOLD   25000.0 // 25kW
```

### للاستخدام الصناعي
```cpp
#define VOLTAGE_MIN_THRESHOLD 360.0   // 360V
#define VOLTAGE_MAX_THRESHOLD 440.0   // 440V
#define CURRENT_MAX_THRESHOLD 100.0   // 100A
#define POWER_MAX_THRESHOLD   50000.0 // 50kW
```

## 💾 حفظ الإعدادات

### حفظ في SPIFFS
```cpp
// حفظ الإعدادات في ملف JSON
bool saveConfiguration() {
  DynamicJsonDocument doc(1024);
  doc["voltage_min"] = voltage_min_threshold;
  doc["voltage_max"] = voltage_max_threshold;
  doc["current_max"] = current_max_threshold;
  doc["blynk_token"] = blynk_token;
  
  File configFile = SPIFFS.open("/config.json", "w");
  serializeJson(doc, configFile);
  configFile.close();
  return true;
}
```

### استرداد الإعدادات
```cpp
// استرداد الإعدادات من الملف
bool loadConfiguration() {
  File configFile = SPIFFS.open("/config.json", "r");
  if (!configFile) return false;
  
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, configFile);
  
  voltage_min_threshold = doc["voltage_min"];
  voltage_max_threshold = doc["voltage_max"];
  current_max_threshold = doc["current_max"];
  blynk_token = doc["blynk_token"].as<String>();
  
  configFile.close();
  return true;
}
```

## 🔄 إعادة تعيين الإعدادات

### إعادة تعيين كاملة
```cpp
void factoryReset() {
  // مسح إعدادات WiFi
  wifiManager.resetSettings();
  
  // مسح ملف الإعدادات
  SPIFFS.remove("/config.json");
  
  // إعادة تعيين القيم الافتراضية
  voltage_min_threshold = VOLTAGE_MIN_THRESHOLD;
  voltage_max_threshold = VOLTAGE_MAX_THRESHOLD;
  current_max_threshold = CURRENT_MAX_THRESHOLD;
  
  // إعادة تشغيل
  ESP.restart();
}
```

---

**💡 نصيحة**: احتفظ بنسخة احتياطية من إعداداتك المخصصة قبل التحديثات!
