# 🔌 نظام مراقبة الطاقة الكهربائية - ESP32 Power Monitor

<div dir="rtl">

## 📋 نظرة عامة

نظام ذكي لمراقبة استهلاك الطاقة الكهربائية باستخدام ESP32 مع إمكانية المراقبة عن بُعد عبر تطبيق Blynk. يقيس الجهد والتيار والقدرة والطاقة المستهلكة في الوقت الفعلي مع إرسال تنبيهات عند تجاوز الحدود المحددة.

## ✨ الميزات الرئيسية

- 📊 **مراقبة في الوقت الفعلي**: قياس الجهد، التيار، القدرة، والطاقة المستهلكة
- 📱 **تحكم عن بُعد**: واجهة Blynk للمراقبة والتحكم عبر الهاتف
- 🚨 **نظام تنبيهات ذكي**: تنبيهات فورية عند تجاوز الحدود الآمنة
- 🔄 **إعادة اتصال تلقائية**: نظام WiFi ذكي مع إعادة اتصال تلقائية
- 💾 **حفظ البيانات**: تسجيل استهلاك الطاقة التراكمي
- 🛡️ **حماية متقدمة**: نظام مراقبة وحماية من الأعطال
- 🎛️ **إعدادات قابلة للتخصيص**: ضبط حدود التنبيه عن بُعد

## 🔧 المتطلبات الأساسية

### الأجهزة المطلوبة
- **ESP32 DevKit v1** أو متوافق
- **مستشعر الجهد ZMPT101B** لقياس الجهد الكهربائي
- **مستشعر التيار ACS712-5A** لقياس التيار
- **أسلاك توصيل** ذكر-أنثى وذكر-ذكر
- **لوحة تجارب (Breadboard)**
- **مقاومة 220Ω** للـ LED المؤشر
- **LED** للحالة (أحمر أو أزرق)

### البرامج المطلوبة
- **PlatformIO IDE** أو **Arduino IDE**
- **تطبيق Blynk** على الهاتف الذكي
- **حساب Blynk Cloud** (مجاني)

## 🚀 دليل التشغيل السريع

### الخطوة 1: إعداد حساب Blynk

1. اذهب إلى [blynk.cloud](https://blynk.cloud) وأنشئ حساب جديد
2. أنشئ Template جديد:
   - **Name**: `Power Monitor`
   - **Hardware**: `ESP32`
   - **Connection Type**: `WiFi`
3. أنشئ Device جديد من Template
4. احفظ **Template ID** و **Auth Token**

### الخطوة 2: إعداد الكود

**بياناتك محدثة بالفعل في المشروع:**
```cpp
Template ID: TMPL6cP443N_M
Template Name: Power Monitor
Auth Token: sGZ8CU6R1gFAii34u08wJEuCBEZ4aDg7
```

**ما تحتاج لتحديثه:**
1. افتح الملف `src/wifi_manager.cpp` (السطر 66-67)
2. حدث بيانات WiFi الخاصة بك:
```cpp
const char* ssid = "اسم_شبكة_الواي_فاي";        // اسم شبكتك
const char* password = "كلمة_مرور_الشبكة";      // كلمة المرور
```

### الخطوة 3: توصيل الأجهزة

#### مستشعر الجهد ZMPT101B:
```
ZMPT101B    →    ESP32
VCC         →    3.3V
GND         →    GND
OUT         →    GPIO36 (A0)
```

#### مستشعر التيار ACS712:
```
ACS712      →    ESP32
VCC         →    5V
GND         →    GND
OUT         →    GPIO39 (A3)
```

#### LED المؤشر:
```
LED         →    ESP32
الطرف الطويل  →    GPIO2 (عبر مقاومة 220Ω)
الطرف القصير →    GND
```

### الخطوة 4: رفع الكود

```bash
# بناء المشروع
pio run

# رفع إلى ESP32
pio run --target upload

# مراقبة الإخراج
pio device monitor
```

### الخطوة 5: إعداد تطبيق Blynk

1. حمل تطبيق **Blynk IoT** من متجر التطبيقات
2. سجل دخول بنفس حساب Blynk Cloud
3. ستجد مشروعك "Power Monitor" في قائمة الأجهزة
4. اضغط على الجهاز لفتح لوحة التحكم

## 📊 Virtual Pins المستخدمة

| Pin | الاسم | الوصف | نوع البيانات |
|-----|-------|--------|-------------|
| V0 | Voltage | الجهد الحالي | Double (V) |
| V1 | Current | التيار الحالي | Double (A) |
| V2 | Power | القدرة المحسوبة | Double (W) |
| V3 | Energy | الطاقة المستهلكة | Double (Wh) |
| V4 | Status | حالة النظام | String |
| V5 | Voltage Min | الحد الأدنى للجهد | Double (V) |
| V6 | Voltage Max | الحد الأقصى للجهد | Double (V) |
| V7 | Current Max | الحد الأقصى للتيار | Double (A) |
| V8 | Reset Energy | إعادة تعيين الطاقة | Integer |
| V9 | Terminal | نافذة الأوامر | String |

## 🎛️ استخدام النظام

### مراقبة القراءات
- **الجهد**: يظهر الجهد الحالي بالفولت (V)
- **التيار**: يظهر التيار المستهلك بالأمبير (A)
- **القدرة**: محسوبة تلقائياً (الجهد × التيار) بالواط (W)
- **الطاقة**: الطاقة التراكمية المستهلكة بالواط/ساعة (Wh)

### ضبط حدود التنبيه
- **الحد الأدنى للجهد**: عادة 200V (حسب شبكتك)
- **الحد الأقصى للجهد**: عادة 250V (حسب شبكتك)
- **الحد الأقصى للتيار**: حسب الحمولة المتوقعة

### استخدام Terminal
اكتب الأوامر التالية في نافذة Terminal:
- `status`: عرض حالة النظام الحالية
- `reset`: إعادة تشغيل النظام

### إعادة تعيين عداد الطاقة
اضغط زر "Reset Energy" لإعادة تعيين عداد الطاقة المستهلكة إلى الصفر.

## 🚨 نظام التنبيهات

### أنواع التنبيهات
1. **جهد منخفض**: عندما ينخفض الجهد عن الحد المحدد
2. **جهد مرتفع**: عندما يرتفع الجهد عن الحد المحدد
3. **تيار مرتفع**: عندما يتجاوز التيار الحد المحدد
4. **قدرة مرتفعة**: عندما تتجاوز القدرة 1000W

### مؤشرات LED
- **ثابت**: النظام يعمل بشكل طبيعي
- **وميض سريع**: تنبيه جهد (مرتفع أو منخفض)
- **وميض ثلاثي**: تنبيه تيار أو قدرة مرتفعة
- **وميض مزدوج**: خطأ في النظام

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال

**عدم الاتصال بـ WiFi:**
- تحقق من صحة اسم الشبكة وكلمة المرور في `src/wifi_manager.cpp`
- تأكد من أن الشبكة 2.4GHz (ESP32 لا يدعم 5GHz)
- اقترب من الراوتر لتحسين الإشارة

**عدم الاتصال بـ Blynk:**
- تحقق من صحة Auth Token في `include/blynk_config.h`
- تأكد من اتصال الإنترنت
- تحقق من حالة خوادم Blynk على status.blynk.cc

### مشاكل القراءات

**قراءات الجهد خاطئة:**
- تحقق من توصيل ZMPT101B
- تأكد من وجود جهد على مدخل المستشعر
- اضبط معامل المعايرة في `include/config.h`

**قراءات التيار خاطئة:**
- تحقق من توصيل ACS712
- تأكد من مرور التيار عبر المستشعر
- اضبط معامل المعايرة

## ⚙️ الإعدادات المتقدمة

### معايرة المستشعرات
في الملف `include/config.h`:
```cpp
#define VOLTAGE_CALIBRATION   234.26  // معامل معايرة الجهد
#define CURRENT_CALIBRATION   185.0   // معامل معايرة التيار
```

### حدود الأمان
```cpp
#define VOLTAGE_MIN_THRESHOLD 200.0   // الحد الأدنى للجهد (V)
#define VOLTAGE_MAX_THRESHOLD 250.0   // الحد الأقصى للجهد (V)
#define CURRENT_MAX_THRESHOLD 4.0     // الحد الأقصى للتيار (A)
#define POWER_MAX_THRESHOLD   1000.0  // الحد الأقصى للقدرة (W)
```

### فترات القياس والإرسال
```cpp
#define SENSOR_READ_INTERVAL  1000    // فترة قراءة المستشعرات (ms)
#define BLYNK_SEND_INTERVAL   2000    // فترة إرسال البيانات (ms)
```

## 🔒 تحذيرات الأمان

⚠️ **تحذيرات مهمة:**
- تعامل مع الكهرباء بحذر شديد
- اقطع الكهرباء قبل التوصيل
- لا تلمس الأسلاك المكشوفة
- استخدم أدوات معزولة
- اختبر النظام على جهد منخفض أولاً
- لا تستخدم النظام في البيئات الرطبة
- تأكد من التأريض الصحيح
- استخدم قواطع الحماية المناسبة

## 🛠️ الصيانة والتحديث

### الصيانة الدورية
- تنظيف المستشعرات من الغبار شهرياً
- فحص التوصيلات كل 3 أشهر
- تحديث البرنامج عند توفر إصدارات جديدة
- مراقبة درجة حرارة ESP32

### تحديث البرنامج
```bash
# تحديث المكتبات
pio lib update

# إعادة بناء ورفع
pio run --target upload
```

## 🔍 مراقبة الأداء

### مؤشرات الأداء
- **استخدام الذاكرة**: يجب أن يكون أقل من 80%
- **قوة الإشارة WiFi**: أفضل من -70 dBm
- **زمن الاستجابة**: أقل من 2 ثانية
- **معدل فقدان البيانات**: أقل من 1%

### سجلات النظام
يمكنك مراقبة سجلات النظام عبر:
- **Serial Monitor**: في PlatformIO
- **Blynk Terminal**: في تطبيق الهاتف
- **Web Dashboard**: عبر Blynk Cloud

## 📈 تحسين الأداء

### نصائح للحصول على أفضل أداء
1. **موقع ESP32**: ضعه في مكان جيد التهوية
2. **قوة الإشارة**: تأكد من قوة إشارة WiFi جيدة
3. **مصدر الطاقة**: استخدم مصدر طاقة مستقر 5V/2A
4. **التداخل**: تجنب الأجهزة التي تسبب تداخل كهرومغناطيسي

### معايرة دقيقة للمستشعرات
```cpp
// للحصول على قراءات دقيقة، قم بمعايرة المستشعرات:
// 1. قس الجهد الفعلي بجهاز قياس دقيق
// 2. اضبط VOLTAGE_CALIBRATION حتى تتطابق القراءات
// 3. كرر نفس العملية للتيار مع CURRENT_CALIBRATION
```

## 🌐 الاستخدام المتقدم

### إضافة مستشعرات إضافية
يمكنك إضافة مستشعرات أخرى مثل:
- **مستشعر درجة الحرارة**: لمراقبة حرارة النظام
- **مستشعر الرطوبة**: لمراقبة البيئة المحيطة
- **مستشعر الضوء**: لتشغيل الإضاءة تلقائياً

### التكامل مع أنظمة أخرى
- **Home Assistant**: للتحكم المنزلي الذكي
- **MQTT**: لإرسال البيانات لخوادم أخرى
- **InfluxDB + Grafana**: لتحليل البيانات المتقدم

## 📞 الدعم والمساعدة

### المشاكل الشائعة وحلولها

**النظام لا يبدأ:**
- تحقق من مصدر الطاقة
- تأكد من سلامة التوصيلات
- اضغط زر Reset على ESP32

**قراءات غير مستقرة:**
- تحقق من التأريض
- تجنب الأسلاك الطويلة
- استخدم مكثفات تنعيم إضافية

**انقطاع الاتصال المتكرر:**
- تحسين موقع ESP32
- تحديث إعدادات الراوتر
- زيادة قوة الإشارة

### الموارد المفيدة
- **وثائق Blynk**: [docs.blynk.io](https://docs.blynk.io)
- **مجتمع Blynk**: [community.blynk.cc](https://community.blynk.cc)
- **وثائق ESP32**: [docs.espressif.com](https://docs.espressif.com)
- **مجتمع Arduino**: [forum.arduino.cc](https://forum.arduino.cc)

### طلب المساعدة
عند طلب المساعدة، يرجى تضمين:
- **إصدار البرنامج**: الموجود في `platformio.ini`
- **رسائل الخطأ**: من Serial Monitor
- **إعدادات النظام**: من ملف `config.h`
- **وصف المشكلة**: بالتفصيل

### المساهمة
نرحب بالمساهمات! يرجى:
- فتح Issue لمناقشة التحسينات
- إرسال Pull Request للتعديلات
- مشاركة تجربتك مع المجتمع
- ترجمة الوثائق للغات أخرى

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

### حقوق الطبع والنشر
```
Copyright (c) 2024 ESP32 Power Monitor Project
Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files...
```

---

## 🎯 الخطوات التالية

بعد إتمام التشغيل الأولي:
1. **اختبر النظام**: تأكد من دقة القراءات
2. **اضبط الحدود**: حسب احتياجاتك الخاصة
3. **راقب الأداء**: لعدة أيام للتأكد من الاستقرار
4. **وثق إعداداتك**: احتفظ بنسخة من الإعدادات
5. **شارك تجربتك**: ساعد المجتمع بتجربتك

**تم تطوير هذا المشروع بحب ❤️ للمجتمع العربي**

**نتمنى لك تجربة ممتعة ومفيدة! 🚀**

</div>
