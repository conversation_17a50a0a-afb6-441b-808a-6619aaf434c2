# نظام مراقبة الطاقة الكهربائية باستخدام ESP32

نظام شامل لمراقبة الطاقة الكهربائية في الوقت الفعلي باستخدام متحكم ESP32 مع التكامل السحابي ودعم تطبيق الهاتف المحمول.

## 🚀 المميزات

- **المراقبة في الوقت الفعلي**: تتبع الجهد والتيار والقدرة واستهلاك الطاقة
- **أجهزة استشعار ذكية**: دعم مستشعر الجهد ZMPT101B ومستشعر التيار ACS712
- **إدارة WiFi**: اتصال تلقائي مع بوابة إعداد سهلة
- **التكامل السحابي**: منصة Blynk IoT للمراقبة والتحكم عن بُعد
- **تطبيق الهاتف**: رسوم بيانية فورية ومقاييس وإشعارات فورية
- **نظام التنبيهات**: حدود قابلة للتخصيص مع إشعارات فورية
- **تتبع الطاقة**: تسجيل استهلاك الطاقة التراكمي
- **تصميم قوي**: استرداد تلقائي ومؤقت مراقبة ومعالجة أخطاء

## 📋 متطلبات الأجهزة

### المكونات الأساسية
- **ESP32 DevKit v1** - المتحكم الرئيسي
- **ZMPT101B** - وحدة مستشعر الجهد المتردد
- **ACS712-5A** - وحدة مستشعر التيار المتردد/المستمر
- **لوحة تجارب وأسلاك توصيل**
- **مصدر طاقة** (5V/3.3V)

### مكونات اختيارية
- **صندوق حماية**
- **كتل طرفية** للتوصيلات الدائمة
- **فيوزات** للأمان

## 🔌 مخطط التوصيل

```
توصيلات ESP32 DevKit v1:
┌─────────────────────────────────────────┐
│                ESP32                    │
│                                         │
│  GPIO36 (A0) ←→ إشارة ZMPT101B          │
│  GPIO39 (A3) ←→ إشارة ACS712            │
│  GPIO2       ←→ LED المدمج              │
│  3.3V        ←→ VCC المستشعرات         │
│  GND         ←→ GND المستشعرات          │
└─────────────────────────────────────────┘

مستشعر الجهد ZMPT101B:
┌─────────────────┐
│    ZMPT101B     │
│                 │
│  VCC ←→ 3.3V    │
│  GND ←→ GND     │
│  OUT ←→ GPIO36  │
│  AC1 ←→ السلك الحي │
│  AC2 ←→ السلك المحايد │
└─────────────────┘

مستشعر التيار ACS712:
┌─────────────────┐
│     ACS712      │
│                 │
│  VCC ←→ 5V      │
│  GND ←→ GND     │
│  OUT ←→ GPIO39  │
│  IP+ ←→ الحمل +  │
│  IP- ←→ الحمل -  │
└─────────────────┘
```

## 🛠️ إعداد البرنامج

### 1. تثبيت PlatformIO
```bash
# تثبيت PlatformIO Core
pip install platformio

# أو استخدم إضافة PlatformIO IDE في VS Code
```

### 2. إعداد المشروع
```bash
# نسخ أو تحميل المشروع
git clone <repository-url>
cd VOLT

# بناء المشروع
pio run

# رفع إلى ESP32
pio run --target upload

# مراقبة الإخراج التسلسلي
pio device monitor
```

### 3. إعداد WiFi
1. تشغيل ESP32
2. الاتصال بشبكة WiFi "PowerMonitor-Setup" (كلمة المرور: 12345678)
3. فتح المتصفح والذهاب إلى ***********
4. إدخال بيانات اعتماد WiFi ورمز Blynk
5. حفظ الإعدادات وإعادة التشغيل

### 4. إعداد تطبيق Blynk

#### إنشاء حساب Blynk
1. تحميل تطبيق Blynk IoT من App Store/Google Play
2. إنشاء حساب في https://blynk.cloud
3. إنشاء جهاز جديد ونسخ رمز المصادقة

#### إعداد الأدوات
أضف هذه الأدوات إلى لوحة تحكم Blynk:

| الأداة | الدبوس الافتراضي | الوصف |
|--------|-------------|-------------|
| مقياس | V0 | عرض الجهد في الوقت الفعلي |
| مقياس | V1 | عرض التيار في الوقت الفعلي |
| مقياس | V2 | عرض القدرة في الوقت الفعلي |
| رسم بياني | V0,V1,V2 | رسوم بيانية للبيانات التاريخية |
| قيمة مُسماة | V3 | استهلاك الطاقة |
| شريط تمرير | V5 | الحد الأدنى لعتبة الجهد |
| شريط تمرير | V6 | الحد الأقصى لعتبة الجهد |
| شريط تمرير | V7 | الحد الأقصى لعتبة التيار |
| زر | V8 | إعادة تعيين عداد الطاقة |
| طرفية | V9 | سجلات النظام والأوامر |

## ⚙️ الإعدادات

### معايرة المستشعرات
يتضمن النظام معايرة تلقائية، لكن يمكنك الضبط الدقيق:

```cpp
// في config.h، اضبط هذه القيم:
#define VOLTAGE_CALIBRATION   234.26  // معايرة مستشعر الجهد
#define CURRENT_CALIBRATION   185.0   // معايرة مستشعر التيار (mV/A)
#define VOLTAGE_OFFSET        2500    // إزاحة الصفر لمستشعر الجهد
#define CURRENT_OFFSET        2500    // إزاحة الصفر لمستشعر التيار
```

### عتبات التنبيه
عتبات الأمان الافتراضية (قابلة للتخصيص عبر تطبيق Blynk):

```cpp
#define VOLTAGE_MIN_THRESHOLD 200.0   // الحد الأدنى للجهد (V)
#define VOLTAGE_MAX_THRESHOLD 250.0   // الحد الأقصى للجهد (V)
#define CURRENT_MAX_THRESHOLD 4.0     // الحد الأقصى للتيار (A)
#define POWER_MAX_THRESHOLD   1000.0  // الحد الأقصى للقدرة (W)
```

## 📱 مميزات تطبيق الهاتف

### المراقبة في الوقت الفعلي
- قراءات مباشرة للجهد والتيار والقدرة
- رسوم بيانية للبيانات التاريخية مع تكبير وتحريك
- تتبع استهلاك الطاقة
- مؤشرات حالة النظام

### التحكم عن بُعد
- عتبات أمان قابلة للتعديل
- إعادة تعيين عداد الطاقة
- أمر إعادة تشغيل النظام
- نسخ احتياطي/استعادة الإعدادات

### الإشعارات
- إشعارات فورية لانتهاك العتبات
- تنبيهات بريد إلكتروني للأحداث الحرجة
- إشعارات SMS (ميزة مدفوعة)
- رسائل تنبيه مخصصة

## 🔧 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

#### مشاكل اتصال WiFi
```
الأعراض: لا يمكن الاتصال بـ WiFi
الحلول:
1. تحقق من بيانات اعتماد WiFi في البوابة الأسيرة
2. تأكد من شبكة 2.4GHz (ESP32 لا يدعم 5GHz)
3. إعادة تعيين إعدادات WiFi: توصيل GPIO0 بـ GND أثناء التشغيل
4. تحقق من توافق الراوتر
```

#### مشاكل قراءة المستشعرات
```
الأعراض: قراءات غير صحيحة أو غير مستقرة
الحلول:
1. تحقق من توصيلات الأسلاك
2. تحقق من مصدر طاقة المستشعر (3.3V لـ ZMPT101B، 5V لـ ACS712)
3. معايرة المستشعرات باستخدام أحمال معروفة
4. تحقق من التداخل الكهرومغناطيسي
5. تأكد من التأريض المناسب
```

#### مشاكل اتصال Blynk
```
الأعراض: WiFi متصل لكن Blynk غير متصل
الحلول:
1. تحقق من رمز المصادقة في الإعدادات
2. تحقق من حالة خادم Blynk
3. تأكد من أن الجهاز متصل في وحدة تحكم Blynk
4. إعادة تعيين اتصال Blynk في أداة الطرفية
```

### أوامر المراقب التسلسلي
استخدم أداة الطرفية في تطبيق Blynk:
- `status` - إظهار حالة النظام
- `reset` - إعادة تشغيل النظام
- `calibrate` - إعادة معايرة المستشعرات

## 📊 مواصفات الأداء

- **نطاق الجهد**: 50V - 300V AC
- **نطاق التيار**: 0A - 5A (ACS712-5A)
- **الدقة**: ±2% (بعد المعايرة)
- **معدل العينات**: 100 عينة/ثانية
- **معدل التحديث**: ثانية واحدة (المستشعرات)، ثانيتان (Blynk)
- **نطاق WiFi**: معيار 802.11 b/g/n
- **استهلاك الطاقة**: ~200mA @ 5V

## 🔒 اعتبارات الأمان

⚠️ **تحذير: يتضمن هذا النظام طاقة AC عالية الجهد. اتبع إرشادات الأمان:**

1. **الأمان الكهربائي**
   - إيقاف الطاقة قبل إجراء التوصيلات
   - استخدام عزل وصناديق مناسبة
   - تثبيت فيوزات وقواطع دوائر مناسبة
   - اتباع الأكواد الكهربائية المحلية

2. **أمان التثبيت**
   - استخدام كهربائي مؤهل للتثبيت الدائم
   - اختبار جميع التوصيلات قبل التشغيل
   - ضمان التأريض المناسب
   - استخدام حماية GFCI حيث مطلوب

3. **الأمان التشغيلي**
   - مراقبة النظام بانتظام
   - الاستجابة للتنبيهات فوراً
   - الحفاظ على أنظمة مراقبة احتياطية
   - الاحتفاظ بجهات اتصال الطوارئ متاحة

## 📈 التحسينات المستقبلية

- دعم **المراقبة متعددة الأطوار**
- **تحليل جودة الطاقة** (التوافقيات، معامل القدرة)
- **التنبؤ بالحمل** باستخدام التعلم الآلي
- **التكامل** مع أنظمة أتمتة المنزل
- **تصدير البيانات** إلى تنسيقات CSV/Excel
- **التحليلات المتقدمة** والتقارير

## 🤝 المساهمة

1. فرع المستودع
2. إنشاء فرع الميزة (`git checkout -b feature/amazing-feature`)
3. تأكيد التغييرات (`git commit -m 'Add amazing feature'`)
4. دفع إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح طلب سحب

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للدعم والأسئلة:
- إنشاء مشكلة على GitHub
- مراجعة قسم استكشاف الأخطاء وإصلاحها
- مراجعة وثائق Blynk
- منتديات مجتمع ESP32

---

**تم البناء بـ ❤️ لمراقبة طاقة موثوقة**
