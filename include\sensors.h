#ifndef SENSORS_H
#define SENSORS_H

#include <Arduino.h>
#include "config.h"

// ============================================================================
// Sensor Management Class for Power Monitoring
// ============================================================================

class PowerSensors {
private:
  // Sensor readings
  float voltage_raw;
  float current_raw;
  float voltage_filtered;
  float current_filtered;
  float power_calculated;
  float energy_total;
  
  // Calibration values
  float voltage_calibration;
  float current_calibration;
  float voltage_offset;
  float current_offset;
  
  // Timing
  unsigned long last_reading_time;
  unsigned long last_energy_update;
  
  // Status
  bool sensors_initialized;
  bool calibration_complete;
  int stable_readings_count;
  
  // Private methods
  float readVoltageRMS();
  float readCurrentRMS();
  void applyLowPassFilter();
  bool validateReadings();
  
public:
  // Constructor
  PowerSensors();
  
  // Initialization
  bool begin();
  void calibrateSensors();
  
  // Reading methods
  bool updateReadings();
  float getVoltage() const { return voltage_filtered; }
  float getCurrent() const { return current_filtered; }
  float getPower() const { return power_calculated; }
  float getEnergy() const { return energy_total; }
  
  // Utility methods
  void resetEnergy();
  bool isDataStable() const;
  bool sensorsHealthy() const;
  
  // Calibration methods
  void setVoltageCalibration(float cal) { voltage_calibration = cal; }
  void setCurrentCalibration(float cal) { current_calibration = cal; }
  void setVoltageOffset(float offset) { voltage_offset = offset; }
  void setCurrentOffset(float offset) { current_offset = offset; }
  
  // Status methods
  String getStatusString() const;
  void printDiagnostics() const;
};

// Global sensor instance
extern PowerSensors sensors;

#endif // SENSORS_H
