#ifndef ALERT_SYSTEM_H
#define ALERT_SYSTEM_H

#include <Arduino.h>
#include "config.h"

// ============================================================================
// Alert and Notification System
// ============================================================================

class AlertSystem {
private:
  // Threshold values
  float voltage_min_threshold;
  float voltage_max_threshold;
  float current_max_threshold;
  float power_max_threshold;
  
  // Alert state tracking
  bool alert_active[8];  // Track active alerts
  unsigned long alert_last_sent[8];  // Last time each alert was sent
  unsigned long alert_cooldown_period;
  
  // Status LED management
  unsigned long last_led_toggle;
  bool led_state;
  int blink_pattern;  // Different patterns for different states
  
  // System status
  SystemState current_state;
  String last_error_message;
  
  // Private methods
  bool isAlertCooldownExpired(AlertType alert);
  void setAlertActive(AlertType alert, bool active);
  void updateStatusLED();
  String getAlertMessage(AlertType alert, float value = 0) const;
  
public:
  // Constructor
  AlertSystem();
  
  // Initialization
  void begin();
  
  // Threshold management
  void setVoltageThresholds(float min_voltage, float max_voltage);
  void setCurrentThreshold(float max_current);
  void setPowerThreshold(float max_power);
  
  float getVoltageMinThreshold() const { return voltage_min_threshold; }
  float getVoltageMaxThreshold() const { return voltage_max_threshold; }
  float getCurrentMaxThreshold() const { return current_max_threshold; }
  float getPowerMaxThreshold() const { return power_max_threshold; }
  
  // Alert checking
  void checkAlerts(float voltage, float current, float power);
  void checkSystemHealth();
  
  // Manual alerts
  void triggerAlert(AlertType alert, const String& custom_message = "");
  void clearAlert(AlertType alert);
  void clearAllAlerts();
  
  // System state management
  void setSystemState(SystemState state);
  SystemState getSystemState() const { return current_state; }
  String getSystemStateString() const;
  
  // Status methods
  bool hasActiveAlerts() const;
  int getActiveAlertCount() const;
  String getActiveAlertsString() const;
  
  // LED status indication
  void setLEDPattern(int pattern);
  void handleLED();
  
  // Error handling
  void setError(const String& error_message);
  String getLastError() const { return last_error_message; }
  void clearError();
  
  // Utility methods
  void printStatus() const;
  String getStatusJSON() const;
};

// LED blink patterns
#define LED_PATTERN_OFF           0
#define LED_PATTERN_SOLID         1
#define LED_PATTERN_SLOW_BLINK    2
#define LED_PATTERN_FAST_BLINK    3
#define LED_PATTERN_DOUBLE_BLINK  4
#define LED_PATTERN_TRIPLE_BLINK  5

// Global alert system instance
extern AlertSystem alertSys;

#endif // ALERT_SYSTEM_H
