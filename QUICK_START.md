# 🚀 Quick Start Guide - ESP32 Power Monitor

Get your power monitoring system up and running in 30 minutes!

## 📦 What You Need

### Hardware Checklist
- [ ] ESP32 DevKit v1
- [ ] ZMPT101B voltage sensor module
- [ ] ACS712-5A current sensor module
- [ ] Breadboard and jumper wires
- [ ] USB cable for ESP32
- [ ] 5V power supply (optional)

### Software Checklist
- [ ] PlatformIO IDE or VS Code with PlatformIO extension
- [ ] Blynk IoT mobile app
- [ ] WiFi network (2.4GHz)

## ⚡ 5-Minute Hardware Setup

### Step 1: Wire the Sensors
```
ESP32 → ZMPT101B:
• 3.3V → VCC
• GND → GND  
• GPIO36 → OUT

ESP32 → ACS712:
• 5V → VCC
• GND → GND
• GPIO39 → OUT
```

### Step 2: Connect Power Lines
```
⚠️ SAFETY FIRST: Turn off power before connecting!

ZMPT101B AC connections:
• AC1 → Live wire (from breaker)
• AC2 → Neutral wire

ACS712 Current path:
• IP+ → Live wire (to load)
• IP- → Live wire (from load)
```

## 💻 5-Minute Software Setup

### Step 1: Flash the Firmware
```bash
# Open terminal in project directory
cd VOLT

# Build and upload
pio run --target upload

# Monitor output
pio device monitor
```

### Step 2: Configure WiFi
1. Power on ESP32
2. Connect phone/laptop to "PowerMonitor-Setup" WiFi
3. Open browser → ***********
4. Enter your WiFi credentials
5. Click "Save" and wait for restart

## 📱 5-Minute Blynk Setup

### Step 1: Create Blynk Account
1. Download Blynk IoT app
2. Sign up at https://blynk.cloud
3. Create new device → ESP32
4. Copy the auth token

### Step 2: Add Widgets
Quick widget setup:

| Widget | Pin | Settings |
|--------|-----|----------|
| Gauge | V0 | 0-300V, "Voltage" |
| Gauge | V1 | 0-5A, "Current" |
| Gauge | V2 | 0-1500W, "Power" |
| Value Display | V3 | "Energy (Wh)" |
| Slider | V5 | 180-250V, "Min Voltage" |
| Slider | V6 | 220-280V, "Max Voltage" |
| Slider | V7 | 0-5A, "Max Current" |
| Button | V8 | "Reset Energy" |

### Step 3: Connect Device
1. Go back to WiFi config (***********)
2. Enter your Blynk auth token
3. Save and restart
4. Check Blynk app - device should be online!

## 🔧 Quick Calibration

### Voltage Calibration
1. Measure actual voltage with multimeter
2. Compare with app reading
3. Adjust `VOLTAGE_CALIBRATION` in config.h if needed

### Current Calibration
1. Use known load (e.g., 100W bulb = ~0.45A at 220V)
2. Compare readings
3. Adjust `CURRENT_CALIBRATION` if needed

## ✅ Verification Checklist

### Hardware Check
- [ ] All connections secure
- [ ] No loose wires
- [ ] Proper insulation
- [ ] Fuses installed
- [ ] ESP32 powered on
- [ ] Status LED blinking

### Software Check
- [ ] Serial monitor shows "System Online"
- [ ] WiFi connected (solid LED)
- [ ] Blynk shows device online
- [ ] Sensor readings appear reasonable
- [ ] Alerts can be triggered

### Safety Check
- [ ] All high voltage connections insulated
- [ ] Enclosure properly closed
- [ ] Emergency stop accessible
- [ ] Circuit breaker functional
- [ ] GFCI protection active

## 🚨 Troubleshooting

### "WiFi won't connect"
```
1. Check 2.4GHz network
2. Verify password
3. Reset: Hold GPIO0 during boot
4. Try different network
```

### "Blynk offline"
```
1. Check auth token
2. Verify internet connection
3. Restart ESP32
4. Check Blynk server status
```

### "Wrong readings"
```
1. Check wiring
2. Verify sensor power (3.3V/5V)
3. Calibrate with known values
4. Check for interference
```

### "No readings"
```
1. Check ADC pins (GPIO36, GPIO39)
2. Verify sensor power
3. Test with multimeter
4. Check serial monitor for errors
```

## 📊 First Test

### Safe Test Setup
1. Use a simple load (LED bulb, 40-100W)
2. Start with low voltage if possible
3. Monitor readings in real-time
4. Verify calculations: P = V × I

### Expected Readings
```
100W LED bulb at 220V:
• Voltage: ~220V
• Current: ~0.45A  
• Power: ~100W
• Energy: Increases over time
```

## 🎯 Next Steps

### Immediate
- [ ] Set appropriate alert thresholds
- [ ] Test push notifications
- [ ] Create energy usage baseline
- [ ] Document your specific calibration values

### Advanced
- [ ] Add more sensors for 3-phase monitoring
- [ ] Integrate with home automation
- [ ] Set up data logging and analysis
- [ ] Create custom Blynk dashboard themes

## 📞 Need Help?

### Quick Fixes
- Restart ESP32: Unplug and reconnect power
- Reset WiFi: Connect GPIO0 to GND during boot
- Factory reset: Hold reset button for 10 seconds

### Get Support
- Check README.md for detailed documentation
- Review wiring diagrams
- Check Blynk community forums
- Create GitHub issue for bugs

---

**🎉 Congratulations! Your power monitor is now live!**

Monitor your energy usage, get alerts for anomalies, and enjoy the peace of mind that comes with real-time power monitoring.

**Remember**: Always prioritize safety when working with electrical systems. When in doubt, consult a qualified electrician.
