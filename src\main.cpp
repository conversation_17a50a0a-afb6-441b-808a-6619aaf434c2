/*
 * ESP32 Power Monitoring System
 *
 * A comprehensive power monitoring solution using ESP32 with:
 * - ZMPT101B voltage sensor for AC voltage measurement
 * - ACS712 current sensor for AC/DC current measurement
 * - WiFiManager for easy network configuration
 * - Blynk IoT Cloud integration for remote monitoring
 * - Real-time alerts and notifications
 * - Energy consumption tracking
 *
 * Hardware Connections:
 * - ZMPT101B voltage sensor -> GPIO36 (A0)
 * - ACS712 current sensor -> GPIO39 (A3)
 * - Built-in LED -> GPIO2 (status indication)
 *
 * Author: ESP32 Power Monitor
 * Version: 1.0
 * Date: 2025
 */

#include <Arduino.h>
#include <esp_task_wdt.h>
#include <esp_system.h>
#include <esp_wifi.h>
#include <driver/adc.h>
#include <esp_adc_cal.h>
#include <soc/soc.h>
#include <soc/rtc_cntl_reg.h>

// Project includes
#include "config.h"
#include "globals.h"
#include "esp32_optimizations.h"
#include "sensors.h"
#include "wifi_manager.h"
#include "alert_system.h"

// ============================================================================
// Global Variables
// ============================================================================

// Timing variables
unsigned long last_sensor_update = 0;
unsigned long last_status_print = 0;
unsigned long last_watchdog_feed = 0;
unsigned long system_start_time = 0;

// System status
bool system_initialized = false;
bool wifi_ready = false;
bool blynk_ready = false;

// Performance monitoring
unsigned long loop_count = 0;
unsigned long max_loop_time = 0;

// ============================================================================
// Function Declarations
// ============================================================================

void initializeSystem();
void handleWiFiConnection();
void handleBlynkConnection();
void updateSensors();
void processAlerts();
void handleStatusLED();
void printSystemStatus();
void feedWatchdog();
void handleSystemRecovery();

// ============================================================================
// Setup Function
// ============================================================================

void setup() {
  // Initialize serial communication first
  Serial.begin(SERIAL_BAUD_RATE);
  delay(1000);

  Serial.println();
  Serial.println("========================================");
  Serial.println("ESP32 Power Monitoring System Starting");
  Serial.println("========================================");

  // Initialize ESP32 optimizations
  esp32opt.initializeESP32();

  // Log system restart reason
  esp32opt.logSystemRestart();

  // Print detailed ESP32 information
  esp32opt.printCPUInfo();
  esp32opt.printMemoryInfo();

  // Record system start time
  system_start_time = millis();

  // Initialize all system components
  initializeSystem();

  Serial.println("System initialization complete");
  Serial.println("Entering main loop...");
  Serial.println("========================================");
}

// ============================================================================
// Main Loop Function
// ============================================================================

void loop() {
  unsigned long loop_start = millis();
  loop_count++;

  // Feed watchdog timer
  feedWatchdog();

  // Handle WiFi connection
  handleWiFiConnection();

  // Handle Blynk connection (only if WiFi is connected)
  if (wifi_ready) {
    handleBlynkConnection();
  }

  // Update sensor readings
  updateSensors();

  // Process alerts and notifications
  processAlerts();

  // Handle status LED
  handleStatusLED();

  // Run Blynk (only if connected)
  if (blynk_ready) {
    // Blynk.run(); // Simplified - direct Blynk call
  }

  // Print system status periodically
  if (millis() - last_status_print > 30000) {  // Every 30 seconds
    printSystemStatus();
    last_status_print = millis();
  }

  // Handle system recovery if needed
  if (alertSys.getSystemState() == STATE_ERROR) {
    handleSystemRecovery();
  }

  // Track maximum loop time for performance monitoring
  unsigned long loop_time = millis() - loop_start;
  if (loop_time > max_loop_time) {
    max_loop_time = loop_time;
  }

  // Small delay to prevent overwhelming the system
  delay(10);
}

// ============================================================================
// System Initialization
// ============================================================================

void initializeSystem() {
  Serial.println("Initializing system components...");

  // Initialize alert system first
  alertSys.begin();
  alertSys.setSystemState(STATE_INITIALIZING);

  // Initialize sensors
  if (!sensors.begin()) {
    alertSys.setError("Failed to initialize sensors");
    return;
  }
  Serial.println("✓ Sensors initialized");

  // Initialize WiFi manager
  if (!wifiMgr.begin()) {
    alertSys.setError("Failed to initialize WiFi manager");
    return;
  }
  Serial.println("✓ WiFi manager initialized");

  // Set initial thresholds
  alertSys.setVoltageThresholds(VOLTAGE_MIN_THRESHOLD, VOLTAGE_MAX_THRESHOLD);
  alertSys.setCurrentThreshold(CURRENT_MAX_THRESHOLD);
  alertSys.setPowerThreshold(POWER_MAX_THRESHOLD);
  Serial.println("✓ Alert thresholds configured");

  system_initialized = true;
  Serial.println("✓ System initialization complete");
}

// ============================================================================
// WiFi Connection Handler
// ============================================================================

void handleWiFiConnection() {
  static unsigned long last_wifi_check = 0;

  // Check WiFi status every 5 seconds
  if (millis() - last_wifi_check > 5000) {
    wifiMgr.handleConnection();

    if (wifiMgr.isConnected() && !wifi_ready) {
      wifi_ready = true;
      alertSys.setSystemState(STATE_BLYNK_CONNECTING);
      Serial.println("✓ WiFi connected successfully");

      // Initialize Blynk with saved credentials
      String token = wifiMgr.getBlynkToken();
      String server = wifiMgr.getBlynkServer();

      if (token.length() > 0) {
        Serial.println("✓ Blynk token configured");
        // blynkMgr.begin(token, server); // Simplified
      } else {
        Serial.println("⚠ No Blynk token configured");
      }

    } else if (!wifiMgr.isConnected() && wifi_ready) {
      wifi_ready = false;
      blynk_ready = false;
      alertSys.setSystemState(STATE_WIFI_CONNECTING);
      alertSys.triggerAlert(ALERT_WIFI_DISCONNECTED, "WiFi connection lost");
      Serial.println("⚠ WiFi disconnected");
    }

    last_wifi_check = millis();
  }
}

// ============================================================================
// Blynk Connection Handler
// ============================================================================

void handleBlynkConnection() {
  static unsigned long last_blynk_check = 0;

  if (!wifi_ready) return;

  // Check Blynk status every 10 seconds
  if (millis() - last_blynk_check > 10000) {
    // Simplified Blynk handling
    if (!blynk_ready) {
      blynk_ready = true;
      alertSys.setSystemState(STATE_RUNNING);
      alertSys.clearAlert(ALERT_BLYNK_DISCONNECTED);
      Serial.println("✓ Blynk connection simulated");
    }

    last_blynk_check = millis();
  }
}

// ============================================================================
// Sensor Update Handler
// ============================================================================

void updateSensors() {
  if (!system_initialized) return;

  // Update sensor readings
  if (sensors.updateReadings()) {
    float voltage = sensors.getVoltage();
    float current = sensors.getCurrent();
    float power = sensors.getPower();
    float energy = sensors.getEnergy();

    // Send data to Blynk if connected
    if (blynk_ready) {
      // Data sending simplified for now
      DEBUG_PRINTF("Data: V=%.1fV, I=%.2fA, P=%.1fW, E=%.2fWh\n",
                   voltage, current, power, energy);
    }

    // Check for sensor health
    if (!sensors.sensorsHealthy()) {
      alertSys.triggerAlert(ALERT_SENSOR_ERROR, "Sensor health check failed");
    }
  }
}

// ============================================================================
// Alert Processing
// ============================================================================

void processAlerts() {
  if (!system_initialized) return;

  // Get current sensor readings
  float voltage = sensors.getVoltage();
  float current = sensors.getCurrent();
  float power = sensors.getPower();

  // Check for threshold violations
  alertSys.checkAlerts(voltage, current, power);

  // Check system health
  alertSys.checkSystemHealth();
}

// ============================================================================
// Status LED Handler
// ============================================================================

void handleStatusLED() {
  alertSys.handleLED();
}

// ============================================================================
// System Status Printer
// ============================================================================

void printSystemStatus() {
  Serial.println("\n=== SYSTEM STATUS ===");
  Serial.printf("Uptime: %lu seconds\n", (millis() - system_start_time) / 1000);
  Serial.printf("Loop count: %lu (Max loop time: %lu ms)\n", loop_count, max_loop_time);

  // System state
  Serial.printf("System State: %s\n", alertSys.getSystemStateString().c_str());

  // Network status
  Serial.printf("WiFi: %s", wifi_ready ? "Connected" : "Disconnected");
  if (wifi_ready) {
    Serial.printf(" (IP: %s, RSSI: %d dBm)",
                  wifiMgr.getIPAddress().c_str(),
                  wifiMgr.getSignalStrength());
  }
  Serial.println();

  Serial.printf("Blynk: %s\n", blynk_ready ? "Connected" : "Disconnected");

  // Sensor readings
  if (sensors.sensorsHealthy()) {
    Serial.printf("Voltage: %.1f V\n", sensors.getVoltage());
    Serial.printf("Current: %.2f A\n", sensors.getCurrent());
    Serial.printf("Power: %.1f W\n", sensors.getPower());
    Serial.printf("Energy: %.3f Wh\n", sensors.getEnergy());
  } else {
    Serial.println("Sensors: ERROR");
  }

  // Alert status
  Serial.printf("Active Alerts: %d\n", alertSys.getActiveAlertCount());
  if (alertSys.hasActiveAlerts()) {
    Serial.printf("Alerts: %s\n", alertSys.getActiveAlertsString().c_str());
  }

  // ESP32 Memory and system status
  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("Min Free Heap: %d bytes\n", ESP.getMinFreeHeap());
  Serial.printf("Largest Free Block: %d bytes\n", ESP.getMaxAllocHeap());
  Serial.printf("PSRAM Size: %d bytes\n", ESP.getPsramSize());
  Serial.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
  Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("Flash Speed: %d MHz\n", ESP.getFlashChipSpeed() / 1000000);
  Serial.printf("Sketch Size: %d bytes\n", ESP.getSketchSize());
  Serial.printf("Free Sketch Space: %d bytes\n", ESP.getFreeSketchSpace());

  Serial.println("====================\n");

  // Reset max loop time counter
  max_loop_time = 0;
}

// ============================================================================
// Watchdog Timer Handler
// ============================================================================

void feedWatchdog() {
  unsigned long current_time = millis();

  // Feed watchdog every 5 seconds
  if (current_time - last_watchdog_feed > 5000) {
    esp32opt.feedTaskWatchdog();
    last_watchdog_feed = current_time;

    // Check memory health periodically
    esp32opt.checkMemoryHealth();
  }
}

// ============================================================================
// System Recovery Handler
// ============================================================================

void handleSystemRecovery() {
  static unsigned long recovery_start = 0;
  static int recovery_attempts = 0;

  if (recovery_start == 0) {
    recovery_start = millis();
    recovery_attempts++;
    alertSys.setSystemState(STATE_RECOVERY);
    Serial.printf("Starting system recovery (attempt %d)...\n", recovery_attempts);
  }

  // Try to recover for 30 seconds
  if (millis() - recovery_start < 30000) {
    // Attempt to reconnect WiFi
    if (!wifi_ready) {
      wifiMgr.reconnect();
      delay(1000);
    }

    // Attempt to reconnect Blynk
    if (wifi_ready && !blynk_ready) {
      Serial.println("Attempting Blynk reconnection...");
      delay(1000);
    }

    // Check if recovery was successful
    if (wifi_ready && blynk_ready && sensors.sensorsHealthy()) {
      alertSys.setSystemState(STATE_RUNNING);
      alertSys.clearError();
      recovery_start = 0;
      recovery_attempts = 0;
      Serial.println("✓ System recovery successful");
      return;
    }
  } else {
    // Recovery timeout
    if (recovery_attempts >= 3) {
      Serial.println("⚠ System recovery failed, restarting...");
      delay(1000);
      ESP.restart();
    } else {
      // Try again
      recovery_start = 0;
      Serial.printf("Recovery attempt %d failed, retrying...\n", recovery_attempts);
    }
  }
}